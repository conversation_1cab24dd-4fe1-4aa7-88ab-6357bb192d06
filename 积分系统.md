# 创作者积分分成系统技术方案 v2.0

## 1. 系统概述

### 1.1 业务背景
为内容创作者提供基于用户互动质量的积分奖励机制，鼓励优质内容创作，提升平台整体内容质量。

### 1.2 核心目标
- **精准激励**：优秀内容单篇可获得约8000积分（约80美元）的激励
- **整数积分**：避免小数计算，确保系统稳定性和计算准确性
- **分类激励**：根据内容类型给予不同比例的积分奖励
- **灵活奖励**：基础积分计算 + 固定精选奖励 + 管理员完全自定义奖励
- **运营调节**：通过全局积分系数灵活调整整体激励力度
- **智能提现**：集成Wise API，自动创建转账单，简化提现流程
- **防刷机制**：有效识别和防范刷量行为

### 1.3 积分兑换比例
- **1积分 = 0.01美元**
- **最低提现：5000积分（50美元）**
- **优秀内容目标：8000积分（80美元）**
- **提现手续费：用户自理（Wise转账约3.30美元+1%手续费）**

### 1.4 内容分类和积分比例
- **原创首发**：100%积分（基准）
- **原创分发**：60%积分（0.6倍系数）
- **授权翻译**：30%积分（0.3倍系数）

## 2. 技术架构设计

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   内容互动层     │    │   积分计算层     │    │   账户管理层     │
│ (多维度互动数据) │ -> │ (智能计算引擎)   │ -> │ (积分账户)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   风控检测层     │              │
         └──────────────│ (防刷/质量评估) │──────────────┘
                        └─────────────────┘
```

### 2.2 核心模块
1. **智能积分引擎**：多维度积分计算和质量评估
2. **互动质量分析**：识别有效互动和刷量行为
3. **内容分级系统**：根据内容质量进行分级奖励
4. **积分账户管理**：整数积分的精确管理
5. **风险控制系统**：实时监控异常行为

## 3. 数据库设计

### 3.1 核心表结构

#### 3.1.1 创作者积分账户表 (creator_points)
```sql
CREATE TABLE creator_points (
    id BIGSERIAL PRIMARY KEY,
    user_id TEXT NOT NULL UNIQUE,
    total_points BIGINT NOT NULL DEFAULT 0,           -- 累计积分(整数)
    available_points BIGINT NOT NULL DEFAULT 0,       -- 可提现积分
    frozen_points BIGINT NOT NULL DEFAULT 0,          -- 冻结积分
    withdrawn_points BIGINT NOT NULL DEFAULT 0,       -- 已提现积分
    last_calculated_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 3.1.2 积分交易记录表 (points_transactions)
```sql
CREATE TABLE points_transactions (
    id BIGSERIAL PRIMARY KEY,
    transaction_id TEXT NOT NULL UNIQUE,
    user_id TEXT NOT NULL,
    content_id TEXT,                                   -- 关联内容ID
    type VARCHAR(20) NOT NULL,                         -- 'earn' | 'withdraw' | 'adjust' | 'bonus'
    sub_type VARCHAR(50),                              -- 具体类型：like, comment, quality_bonus等
    amount BIGINT NOT NULL,                            -- 积分金额(整数)
    balance BIGINT NOT NULL,                           -- 交易后余额
    description VARCHAR(500),
    metadata JSONB,                                    -- 扩展信息
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 3.1.3 内容积分统计表 (content_points_stats)
```sql
CREATE TABLE content_points_stats (
    id BIGSERIAL PRIMARY KEY,
    content_id TEXT NOT NULL UNIQUE,
    author_id TEXT NOT NULL,
    
    -- 积分计算结果
    base_points BIGINT DEFAULT 0,                      -- 基础积分
    feature_bonus BIGINT DEFAULT 0,                    -- 精选奖励
    manual_bonus BIGINT DEFAULT 0,                     -- 管理员手动奖励
    content_type_adjusted_points BIGINT DEFAULT 0,     -- 内容类型调整后积分
    total_points BIGINT DEFAULT 0,                     -- 总积分
    
    -- 内容属性
    content_type VARCHAR(20) DEFAULT 'original_first', -- 'original_first' | 'original_repost' | 'authorized_translation'
    is_featured BOOLEAN DEFAULT FALSE,
    
    -- 计算快照时间
    last_calculated_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 注释：互动数据(点赞、评论、阅读、分享)直接从现有业务表中实时查询
-- 这避免了数据冗余和同步问题
```

#### 3.1.4 管理员手动奖励记录表 (manual_bonus_records)
```sql
CREATE TABLE manual_bonus_records (
    id BIGSERIAL PRIMARY KEY,
    content_id TEXT NOT NULL,
    author_id TEXT NOT NULL,
    admin_id TEXT NOT NULL,
    bonus_points BIGINT NOT NULL,
    reason TEXT NOT NULL,
    bonus_type VARCHAR(20) DEFAULT 'quality',          -- 'quality' | 'special' | 'event' | 'correction'
    status VARCHAR(20) DEFAULT 'active',               -- 'active' | 'revoked'
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 3.1.5 积分配置表 (points_config)
```sql
CREATE TABLE points_config (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(50) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description VARCHAR(200),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 初始化配置数据
INSERT INTO points_config (config_key, config_value, description) VALUES
-- 基础积分权重 (保持稳定，不轻易修改)
('points_per_like', '3', '每个点赞基础积分'),
('points_per_comment', '10', '每个评论基础积分'),
('points_per_view', '1', '每个有效阅读基础积分'),
('points_per_share', '15', '每个分享基础积分'),

-- 运营调整系数 (运营可灵活调整)
('global_points_multiplier', '150', '全局积分系数(百分比，100=无调整)'),



-- 内容类型系数
('content_type_original_first', '100', '原创首发积分系数(百分比)'),
('content_type_original_repost', '60', '原创分发积分系数(百分比)'),
('content_type_authorized_translation', '30', '授权翻译积分系数(百分比)'),

-- 特殊奖励 (基础配置)
('featured_bonus', '2000', '精选内容基础奖励积分'),

-- 提现配置
('min_withdrawal_points', '1000', '最低提现积分'),
('max_withdrawal_points', '100000', '单次最高提现积分'),
('points_to_usd_rate', '100', '积分美元兑换比例(积分/美元)'),
('withdrawal_fee_fixed', '330', '固定手续费(美分)'),
('withdrawal_fee_rate', '100', '手续费比例(万分比)');
```

#### 3.1.6 提现申请表 (withdrawal_requests)
```sql
CREATE TABLE withdrawal_requests (
    id BIGSERIAL PRIMARY KEY,
    request_id TEXT NOT NULL UNIQUE,
    user_id TEXT NOT NULL,
    amount_points BIGINT NOT NULL,                     -- 提现积分
    amount_usd NUMERIC(10,2) NOT NULL,                 -- 对应美元金额
    target_currency VARCHAR(3) NOT NULL,               -- 目标货币
    target_amount NUMERIC(15,2),                       -- 目标货币金额
    status VARCHAR(20) DEFAULT 'pending',              -- pending | wise_created | approved | rejected | completed
    withdrawal_method VARCHAR(50) DEFAULT 'wise',     -- 提现方式
    
    -- Wise集成字段
    wise_transfer_id TEXT,                             -- Wise转账ID
    wise_quote_id TEXT,                                -- Wise报价ID
    wise_recipient_id BIGINT,                          -- Wise收款人ID
    wise_status VARCHAR(50),                           -- Wise转账状态
    wise_fee NUMERIC(10,2),                            -- Wise手续费
    wise_exchange_rate NUMERIC(15,6),                  -- 汇率
    
    -- 收款人信息
    recipient_info JSONB NOT NULL,                     -- 收款人详细信息
    
    -- 审核信息
    admin_id TEXT,                                     -- 审核管理员
    admin_notes TEXT,                                  -- 审核备注
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 添加索引
CREATE INDEX idx_withdrawal_wise_transfer ON withdrawal_requests(wise_transfer_id);
CREATE INDEX idx_withdrawal_status ON withdrawal_requests(status);
CREATE INDEX idx_withdrawal_user_status ON withdrawal_requests(user_id, status);
```

## 4. 积分计算策略

### 4.1 多层次积分计算模型

#### 4.1.1 基础积分规则
```javascript
// 基础积分权重（整数）
const BASE_POINTS = {
  like: 3,         // 点赞：3积分
  comment: 10,     // 评论：10积分  
  view: 1,         // 有效阅读：1积分
  share: 15        // 分享：15积分
};



// 内容类型系数
const CONTENT_TYPE_MULTIPLIER = {
  original_first: 1.0,              // 原创首发：100%积分
  original_repost: 0.6,             // 原创分发：60%积分
  authorized_translation: 0.3       // 授权翻译：30%积分
};
```



### 4.2 积分计算公式

#### 4.2.1 简化积分计算
```javascript
function calculateTotalPoints(contentStats, manualBonus = 0, globalMultiplier = 1.5) {
  // 1. 基础积分计算
  const basePoints = 
    contentStats.like_count * BASE_POINTS.like +
    contentStats.comment_count * BASE_POINTS.comment +
    contentStats.view_count * BASE_POINTS.view +
    contentStats.share_count * BASE_POINTS.share;
  
  // 2. 应用全局积分系数
  const adjustedBasePoints = Math.floor(basePoints * globalMultiplier);
  
  // 3. 内容类型调整
  const contentTypeMultiplier = CONTENT_TYPE_MULTIPLIER[contentStats.content_type];
  const contentTypeAdjustedPoints = Math.floor(adjustedBasePoints * contentTypeMultiplier);
  
  // 4. 固定奖励
  let bonusPoints = 0;
  
  // 精选奖励（固定一次性奖励，不受系数影响）
  if (contentStats.is_featured) {
    const featuredBonus = 2000;  // 固定20美元精选奖励
    bonusPoints += Math.floor(featuredBonus * contentTypeMultiplier);
  }
  
  // 管理员手动奖励（完全自定义，不受任何系数影响）
  bonusPoints += manualBonus;
  
  return {
    basePoints,
    adjustedBasePoints,
    contentTypeMultiplier,
    contentTypeAdjustedPoints,
    featuredBonus: contentStats.is_featured ? Math.floor(2000 * contentTypeMultiplier) : 0,
    manualBonus,
    totalPoints: contentTypeAdjustedPoints + bonusPoints
  };
}
```

### 4.3 不同类型内容积分计算示例

#### 4.3.1 原创首发内容（目标7000积分）
```javascript
// 示例：优秀原创首发文章
const originalFirstExample = {
  view_count: 2000,        // 2000阅读
  like_count: 200,         // 200点赞 (10%互动率)
  comment_count: 50,       // 50评论 (2.5%评论率)
  share_count: 30,         // 30分享
  content_type: 'original_first',
  is_featured: true        // 获得精选
};

// 积分计算过程：
// 1. 基础积分：2000*1 + 200*3 + 50*10 + 30*15 = 3550
// 2. 应用全局系数：3550 * 1.5 = 5325
// 3. 内容类型调整：5325 * 1.0 = 5325 (原创首发100%)
// 4. 精选奖励：2000 * 1.0 = 2000 (固定20美元)
// 5. 管理员奖励：1000 (完全自定义)
// 6. 总计：5325 + 2000 + 1000 = 8325 ≈ 8000积分 ✓
```

#### 4.3.2 原创分发内容（目标5000积分）
```javascript
// 示例：优秀原创分发文章
const originalRepostExample = {
  ...originalFirstExample,
  content_type: 'original_repost'
};

// 积分计算过程：
// 1. 基础积分：3550
// 2. 应用全局系数：3550 * 1.5 = 5325
// 3. 内容类型调整：5325 * 0.6 = 3195 (原创分发60%)
// 4. 精选奖励：2000 * 0.6 = 1200 (固定奖励按内容类型调整)
// 5. 管理员奖励：600 (完全自定义)
// 6. 总计：3195 + 1200 + 600 = 4995 ≈ 5000积分
```

#### 4.3.3 授权翻译内容（目标2500积分）
```javascript
// 示例：优秀授权翻译文章
const authorizedTranslationExample = {
  ...originalFirstExample,
  content_type: 'authorized_translation'
};

// 积分计算过程：
// 1. 基础积分：3550
// 2. 应用全局系数：3550 * 1.5 = 5325
// 3. 内容类型调整：5325 * 0.3 = 1597 (授权翻译30%)
// 4. 精选奖励：2000 * 0.3 = 600 (固定奖励按内容类型调整)
// 5. 管理员奖励：300 (完全自定义)
// 6. 总计：1597 + 600 + 300 = 2497 ≈ 2500积分
```

## 5. 系统实现方案

### 5.1 核心服务模块

#### 5.1.1 积分计算引擎 (PointsCalculationEngine)
```typescript
interface ContentMetrics {
  contentId: string;
  authorId: string;
  likeCount: number;
  commentCount: number;
  viewCount: number;
  shareCount: number;
  uniqueViewers: number;
  avgReadTime: number;
  bounceRate: number;
  contentType: 'original_first' | 'original_repost' | 'authorized_translation';
  isFeatured: boolean;
}

interface PointsResult {
  basePoints: number;
  contentType: string;
  contentTypeMultiplier: number;
  contentTypeAdjustedPoints: number;
  featuredBonus: number;
  manualBonus: number;
  totalPoints: number;
}

class PointsCalculationEngine {
  // 从现有业务表获取内容互动数据
  static async getContentMetrics(contentId: string): Promise<ContentMetrics> {
    const [likes, comments, views, shares, contentInfo] = await Promise.all([
      // 从现有业务表查询，避免数据冗余
      db.query('SELECT COUNT(*) as count FROM likes WHERE content_id = ?', [contentId]),
      db.query('SELECT COUNT(*) as count FROM comments WHERE content_id = ?', [contentId]), 
      db.query('SELECT COUNT(*) as count, COUNT(DISTINCT user_id) as unique_count, AVG(read_time) as avg_time FROM views WHERE content_id = ?', [contentId]),
      db.query('SELECT COUNT(*) as count FROM shares WHERE content_id = ?', [contentId]),
      db.query('SELECT author_id, content_type, is_featured FROM contents WHERE id = ?', [contentId])
    ]);
    
    return {
      contentId,
      authorId: contentInfo.author_id,
      likeCount: likes.count,
      commentCount: comments.count,
      viewCount: views.count,
      shareCount: shares.count,
      uniqueViewers: views.unique_count,
      avgReadTime: views.avg_time || 0,
      bounceRate: this.calculateBounceRate(contentId),
      contentType: contentInfo.content_type,
      isFeatured: contentInfo.is_featured
    };
  }
  
  // 计算内容总积分
  static calculateContentPoints(metrics: ContentMetrics, manualBonus?: number): PointsResult;
  
  // 计算增量积分（用于实时更新）
  static calculateIncrementalPoints(
    interactionType: string,
    contentAttributes: object
  ): number;
}
```

#### 5.1.2 积分账户服务 (PointsAccountService)
```typescript
class PointsAccountService {
  // 初始化用户积分账户
  static async initializeAccount(userId: string): Promise<void>;
  
  // 更新积分余额
  static async updateBalance(
    userId: string,
    amount: number, 
    type: string,
    description: string,
    metadata?: object
  ): Promise<void>;
  
  // 查询积分余额
  static async getBalance(userId: string): Promise<{
    totalPoints: number;
    availablePoints: number;
    frozenPoints: number;
    withdrawnPoints: number;
  }>;
  
  // 通过Wise API创建转账单
  static async createWiseTransfer(
    userId: string,
    amountUsd: number,
    recipientInfo: RecipientInfo
  ): Promise<{
    transferId: string;
    quoteId: string;
    status: string;
    totalFee: number;
    targetAmount: number;
    estimatedDelivery: string;
  }> {
    try {
      // 1. 创建收款人账户
      const recipientAccount = await this.createWiseRecipient(recipientInfo);
      
      // 2. 创建报价
      const quote = await this.createWiseQuote(amountUsd, recipientInfo.currency);
      
      // 3. 创建转账单（待审核状态）
      const transfer = await this.createWiseTransferOrder(quote.id, recipientAccount.id);
      
      return {
        transferId: transfer.id,
        quoteId: quote.id,
        status: transfer.status,
        totalFee: quote.fee.total,
        targetAmount: quote.targetAmount,
        estimatedDelivery: quote.paymentOptions[0].formattedEstimatedDelivery
      };
    } catch (error) {
      throw new Error(`Wise转账创建失败: ${error.message}`);
    }
  }
  
  // 创建Wise收款人
  // 参考文档: https://docs.wise.com/api-docs/api-reference/recipient-account
  static async createWiseRecipient(recipientInfo: RecipientInfo): Promise<any> {
    const response = await fetch('https://api.wise.com/v1/accounts', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.WISE_API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        currency: recipientInfo.currency,
        type: recipientInfo.accountType,
        profile: process.env.WISE_PROFILE_ID,
        accountHolderName: recipientInfo.fullName,
        details: recipientInfo.bankDetails
      })
    });
    
    return await response.json();
  }
  
  // 创建Wise报价
  // 参考文档: https://docs.wise.com/api-docs/api-reference/quote
  static async createWiseQuote(amountUsd: number, targetCurrency: string): Promise<any> {
    const response = await fetch(`https://api.wise.com/v3/profiles/${process.env.WISE_PROFILE_ID}/quotes`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.WISE_API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sourceCurrency: 'USD',
        targetCurrency: targetCurrency,
        sourceAmount: amountUsd
      })
    });
    
    return await response.json();
  }
  
  // 创建Wise转账单
  // 参考文档: https://docs.wise.com/api-docs/api-reference/transfer
  static async createWiseTransferOrder(quoteId: string, recipientAccountId: number): Promise<any> {
    const response = await fetch('https://api.wise.com/v1/transfers', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.WISE_API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        targetAccount: recipientAccountId,
        quoteUuid: quoteId,
        customerTransactionId: `points_withdrawal_${Date.now()}`,
        details: {
          reference: "积分系统提现"
        }
      })
    });
    
    return await response.json();
  }
  
  // 积分转账（系统内部）
  static async transferPoints(
    fromUserId: string,
    toUserId: string,
    amount: number,
    reason: string
  ): Promise<void>;
}
```

#### 5.1.3 管理员奖励服务 (AdminBonusService)
```typescript
class AdminBonusService {
  // 添加手动奖励
  static async addManualBonus(
    contentId: string,
    adminId: string,
    bonusPoints: number,
    reason: string,
    bonusType: 'quality' | 'special' | 'event' | 'correction' = 'quality'
  ): Promise<{
    success: boolean;
    bonusId: string;
    newTotalPoints: number;
  }> {
    // 1. 记录奖励记录
    const bonusRecord = await db.query(`
      INSERT INTO manual_bonus_records 
      (content_id, author_id, admin_id, bonus_points, reason, bonus_type)
      VALUES (?, ?, ?, ?, ?, ?)
      RETURNING id
    `, [contentId, await this.getAuthorId(contentId), adminId, bonusPoints, reason, bonusType]);
    
    // 2. 更新内容积分统计
    await this.updateContentPointsStats(contentId);
    
    // 3. 更新用户积分余额
    const newTotal = await this.updateUserPoints(contentId, bonusPoints);
    
    return {
      success: true,
      bonusId: bonusRecord.id,
      newTotalPoints: newTotal
    };
  }
  
  // 撤销手动奖励
  static async revokeManualBonus(
    bonusId: string,
    adminId: string,
    reason: string
  ): Promise<{
    success: boolean;
    revokedPoints: number;
  }> {
    // 1. 标记奖励为已撤销
    const bonus = await db.query(`
      UPDATE manual_bonus_records 
      SET status = 'revoked', updated_at = NOW()
      WHERE id = ? AND status = 'active'
      RETURNING bonus_points, content_id
    `, [bonusId]);
    
    if (!bonus) {
      throw new Error('奖励记录不存在或已撤销');
    }
    
    // 2. 重新计算积分
    await this.updateContentPointsStats(bonus.content_id);
    
    // 3. 调整用户积分
    await this.updateUserPoints(bonus.content_id, -bonus.bonus_points);
    
    return {
      success: true,
      revokedPoints: bonus.bonus_points
    };
  }
  
  // 获取内容的所有手动奖励
  static async getManualBonuses(contentId: string): Promise<{
    totalBonus: number;
    records: ManualBonusRecord[];
  }> {
    const records = await db.query(`
      SELECT * FROM manual_bonus_records 
      WHERE content_id = ? AND status = 'active'
      ORDER BY created_at DESC
    `, [contentId]);
    
    const totalBonus = records.reduce((sum, record) => sum + record.bonus_points, 0);
    
    return { totalBonus, records };
  }
}
```

### 5.2 API 接口设计

#### 5.2.1 积分查询接口
```
GET /api/creator/points?userId={userId}
响应: {
  success: true,
  data: {
    totalPoints: 25000,
    availablePoints: 20000,
    frozenPoints: 5000,
    withdrawnPoints: 15000,
    pointsToUsd: 250.00,
    recentTransactions: [
      {
        id: "tx_001",
        type: "earn",
        subType: "quality_bonus",
        amount: 1500,
        contentId: "article_123",
        description: "优秀文章质量奖励",
        createdAt: "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

#### 5.2.2 内容积分详情接口
```
GET /api/creator/content-points?contentId={contentId}
响应: {
  success: true,
  data: {
    contentId: "article_123",
    totalPoints: 6550,
        breakdown: {
      basePoints: 3550,
      contentType: "original_first",
      contentTypeMultiplier: 1.0,
      contentTypeAdjustedPoints: 3550,
      featuredBonus: 2000,
      manualBonus: 1000
    },
    // 实时从业务表获取的互动数据
    liveMetrics: {
      likeCount: 180,      // 实时查询 likes 表
      commentCount: 42,    // 实时查询 comments 表  
      viewCount: 1850,     // 实时查询 views 表
      shareCount: 28,      // 实时查询 shares 表
      uniqueViewers: 1665, // 实时计算独立访客
      avgReadTime: 185     // 实时计算平均阅读时长
    },
    lastCalculated: "2024-01-15T02:00:00Z",  // 上次积分计算时间
    dataFreshness: "realtime"                // 数据实时性标识
  }
}
```

#### 5.2.3 提现申请接口
```
#### 5.2.3 简化的提现申请接口（集成Wise）
```
POST /api/creator/withdrawal
请求: {
  userId: "user_123",
  amountPoints: 1500,
  targetCurrency: "CNY",
  recipientInfo: {
    fullName: "张三",
    accountType: "chinese_card",  // Wise支持的账户类型
    bankDetails: {
      firstLine: "北京市朝阳区建国路1号",
      city: "北京",
      postCode: "100000",
      cnaps: "************",      // 中国银行代码
      accountNumber: "6228480402564890018"
    }
  }
}
响应: {
  success: true,
  data: {
    requestId: "wd_001",
    amountPoints: 1500,
    amountUsd: 15.00,
    wiseTransfer: {
      transferId: "wise_transfer_456",
      quoteId: "wise_quote_123",
      status: "waiting_recipient_input_to_proceed",  // Wise状态
      totalFee: 3.42,
      exchangeRate: 7.2456,
      targetAmount: 83.95,      // 实际到账金额（人民币）
      estimatedDelivery: "by Jan 16"
    },
    status: "wise_created",     // 系统状态：已创建Wise转账单
    message: "转账单已创建，等待管理员审核后执行转账"
  }
}

GET /api/creator/withdrawal/{requestId}
响应: {
  success: true,
  data: {
    requestId: "wd_001",
    status: "wise_created",
    wiseTransfer: {
      transferId: "wise_transfer_456",
      wiseStatus: "waiting_recipient_input_to_proceed",
      canBeFunded: true,        // 是否可以执行转账
      statusMessage: "等待管理员审核"
    },
    timeline: [
      {
        status: "pending",
        timestamp: "2024-01-15T10:30:00Z",
        message: "提现申请已提交"
      },
      {
        status: "wise_created", 
        timestamp: "2024-01-15T10:31:00Z",
        message: "Wise转账单已创建"
      }
    ]
  }
}
```

#### 5.2.4 管理员提现审核接口
```
POST /api/admin/withdrawal/{requestId}/approve
请求: {
  adminId: "admin_001",
  notes: "用户信息验证通过，批准提现"
}
响应: {
  success: true,
  data: {
    requestId: "wd_001",
    wiseTransfer: {
      transferId: "wise_transfer_456",
      fundingStatus: "COMPLETED",    // 转账已执行
      wiseStatus: "outgoing_payment_sent"
    },
    message: "提现已批准并执行转账"
  }
}

POST /api/admin/withdrawal/{requestId}/reject  
请求: {
  adminId: "admin_001",
  reason: "银行信息不完整",
  notes: "请提供完整的银行账户信息"
}
响应: {
  success: true,
  data: {
    requestId: "wd_001",
    wiseTransfer: {
      transferId: "wise_transfer_456",
      cancelled: true
    },
    pointsRefunded: 1500,
    message: "提现已拒绝，积分已退回"
  }
}

GET /api/admin/withdrawals?status=wise_created
响应: {
  success: true,
  data: {
    withdrawals: [
      {
        requestId: "wd_001",
        userId: "user_123",
        amountPoints: 1500,
        amountUsd: 15.00,
        targetCurrency: "CNY",
        targetAmount: 83.95,
        recipientName: "张三",
        wiseTransferId: "wise_transfer_456",
        createdAt: "2024-01-15T10:30:00Z",
        canApprove: true
      }
    ],
    total: 1
  }
}
```

#### 5.2.5 管理员奖励接口
```
POST /api/admin/manual-bonus
请求: {
  contentId: "article_123",
  adminId: "admin_001",
  bonusPoints: 1000,
  reason: "内容质量优秀，思路清晰，对读者有很大帮助",
  bonusType: "quality"
}
响应: {
  success: true,
  data: {
    bonusId: "bonus_001",
    contentId: "article_123",
    bonusPoints: 1000,
    newTotalPoints: 6550,
    message: "奖励已成功发放"
  }
}

GET /api/admin/manual-bonus?contentId={contentId}
响应: {
  success: true,
  data: {
    contentId: "article_123",
    totalManualBonus: 1000,
    records: [
      {
        id: "bonus_001",
        adminId: "admin_001",
        bonusPoints: 1000,
        reason: "内容质量优秀，思路清晰，对读者有很大帮助",
        bonusType: "quality",
        status: "active",
        createdAt: "2024-01-15T14:30:00Z"
      }
    ]
  }
}

DELETE /api/admin/manual-bonus/{bonusId}
请求: {
  reason: "误操作，需要撤销"
}
响应: {
  success: true,
  data: {
    bonusId: "bonus_001",
    revokedPoints: 1000,
    message: "奖励已成功撤销"
  }
}
```

## 6. 定时任务设计

### 6.1 任务调度计划
```typescript
class PointsTaskScheduler {
  // 每5分钟：更新活跃内容统计
  @Cron('0 */5 * * * *')
  async updateActiveContentStats(): Promise<void> {
    // 更新最近24小时内有互动的内容统计
  }
  
  // 每小时：重算热门内容积分
  @Cron('0 0 * * * *')
  async recalculateHotContent(): Promise<void> {
    // 重新计算阅读量>1000的内容积分
  }
  
  // 每日凌晨2点：全量积分结算
  @Cron('0 0 2 * * *')
  async dailyPointsSettlement(): Promise<void> {
    // 1. 获取需要重新计算的内容列表
    const contents = await this.getActiveContents();
    
    for (const content of contents) {
      // 2. 从现有业务表实时获取互动数据
      const metrics = await PointsCalculationEngine.getContentMetrics(content.id);
      
      // 3. 获取管理员手动奖励
      const manualBonus = await AdminBonusService.getManualBonuses(content.id);
      
      // 4. 计算最新积分
      const pointsResult = PointsCalculationEngine.calculateContentPoints(metrics, manualBonus.totalBonus);
      
              // 5. 更新积分统计表（只存储计算结果，不存储原始互动数据）
        await this.updateContentPointsStats(content.id, pointsResult);
        
        // 6. 更新用户积分余额
        await this.updateUserPointsBalance(content.author_id, pointsResult);
          }
      
      // 7. 生成积分报表和异常检测
      await this.generateReportsAndAlerts();
  }
  
  // 每周一凌晨：系统健康检查
  @Cron('0 0 1 * * 1')
  async weeklyHealthCheck(): Promise<void> {
    // 1. 积分一致性检查
    // 2. 清理过期数据
    // 3. 优化数据库索引
  }
}
```

## 7. 提现费用计算

### 7.1 费用结构
基于Wise转账费用标准：
- **固定费用**：3.30美元
- **比例费用**：汇款金额的1%
- **总费用**：3.30 + 汇款金额 × 1%

### 7.2 动态费用计算（集成Wise API）
```javascript
// 使用Wise API实时计算费用
// 参考文档: https://docs.wise.com/api-docs/api-reference/quote
async function calculateWithdrawalFeeWithWise(amountUsd) {
  try {
    // 调用Wise Quote API
    const response = await fetch('https://api.wise.com/v3/quotes', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${WISE_API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sourceCurrency: 'USD',
        targetCurrency: 'CNY', // 或用户选择的目标货币
        sourceAmount: amountUsd
      })
    });
    
    const quote = await response.json();
    
    return {
      wiseQuoteId: quote.id,
      totalFee: quote.fee.total,
      wiseFee: quote.fee.transferwise,
      exchangeRate: quote.rate,
      actualReceive: quote.targetAmount,
      estimatedDelivery: quote.paymentOptions[0].formattedEstimatedDelivery
    };
  } catch (error) {
    // 降级到静态计算
    return calculateStaticWithdrawalFee(amountUsd);
  }
}

// 静态费用计算（备用方案）
function calculateStaticWithdrawalFee(amountUsd) {
  const fixedFee = 3.30;
  const rateFee = amountUsd * 0.01;
  const totalFee = fixedFee + rateFee;
  
  return {
    fixedFee,
    rateFee,
    totalFee,
    actualReceive: Math.max(0, amountUsd - totalFee)
  };
}

// 实时费用示例
const dynamicExamples = [
  { amount: 10, wiseFee: 4.12, receive: 5.88 },   // 实时API费用
  { amount: 50, wiseFee: 3.67, receive: 46.33 },  // 实时API费用
  { amount: 100, wiseFee: 4.15, receive: 95.85 }, // 实时API费用
];
```

### 7.3 费用优化建议
```typescript
class WithdrawalFeeOptimizer {
  // 计算最优提现金额
  static calculateOptimalAmount(targetReceive: number): {
    recommendedAmount: number;
    feePercentage: number;
    actualReceive: number;
  } {
    // 建议用户积攒到50美元以上再提现，费用比例更合理
    const minRecommended = 50;
    const amount = Math.max(targetReceive + 4.30, minRecommended);
    
    return {
      recommendedAmount: amount,
      feePercentage: ((3.30 + amount * 0.01) / amount * 100),
      actualReceive: amount - 3.30 - amount * 0.01
    };
  }
}
```

## 8. 动态积分配置管理

### 8.1 运营调整系数设计
```typescript
class OperationalMultiplierConfig {
  // 获取当前全局积分系数
  static async getGlobalMultiplier(): Promise<number> {
    const config = await this.loadConfig();
    return parseInt(config.global_points_multiplier) / 100;
  }
  
  // 应用全局系数的积分计算
  static async calculateAdjustedBasePoints(basePoints: number): Promise<number> {
    const multiplier = await this.getGlobalMultiplier();
    return Math.floor(basePoints * multiplier);
  }
  
  // 更新全局积分系数
  static async updateGlobalMultiplier(
    percentage: number,
    adminId: string,
    reason: string
  ): Promise<void> {
    await db.query(`
      UPDATE points_config 
      SET config_value = ?, updated_at = NOW() 
      WHERE config_key = 'global_points_multiplier'
    `, [percentage.toString()]);
    
    // 记录变更日志
    await this.logMultiplierChange('global', percentage, adminId, reason);
  }
}
```

### 8.2 简化的运营系数接口
```typescript
// 调整全局积分系数
POST /api/admin/global-multiplier
{
  percentage: 150,  // 150% = 提升50%
  reason: "用户增长初期，提升整体激励"
}

// 获取当前全局系数
GET /api/admin/global-multiplier
响应: {
  success: true,
  data: {
    currentMultiplier: 150,    // 当前全局系数
    lastUpdated: "2024-01-15T10:30:00Z",
    lastUpdatedBy: "admin_001"
  }
}

// 系数调整历史
GET /api/admin/global-multiplier/history
响应: {
  success: true,
  data: [
    {
      oldValue: 100,
      newValue: 150,
      adminId: "admin_001", 
      reason: "用户增长初期激励",
      createdAt: "2024-01-15T10:30:00Z"
    },
    {
      oldValue: 150,
      newValue: 120,
      adminId: "admin_002",
      reason: "用户活跃度提升，适当调低激励",
      createdAt: "2024-02-01T09:15:00Z"
    }
  ]
}
```

## 9. 内容类型管理

### 8.1 内容类型定义
```typescript
enum ContentType {
  ORIGINAL_FIRST = 'original_first',           // 原创首发
  ORIGINAL_REPOST = 'original_repost',         // 原创分发
  AUTHORIZED_TRANSLATION = 'authorized_translation'  // 授权翻译
}

interface ContentTypeConfig {
  multiplier: number;        // 积分系数
  description: string;       // 类型描述
  requirements: string[];    // 要求说明
}

const CONTENT_TYPE_CONFIG: Record<ContentType, ContentTypeConfig> = {
  [ContentType.ORIGINAL_FIRST]: {
    multiplier: 1.0,
    description: '原创首发内容',
    requirements: ['首次发布', '原创内容', '独家发布']
  },
  [ContentType.ORIGINAL_REPOST]: {
    multiplier: 0.6,
    description: '原创分发内容',
    requirements: ['原创内容', '已在其他平台发布', '内容质量达标']
  },
  [ContentType.AUTHORIZED_TRANSLATION]: {
    multiplier: 0.3,
    description: '授权翻译内容',
    requirements: ['获得授权', '翻译质量达标', '标注原作者']
  }
};
```

### 8.2 内容类型审核流程
```typescript
class ContentTypeManager {
  // 内容类型审核
  static async reviewContentType(
    contentId: string,
    proposedType: ContentType,
    evidence: ContentTypeEvidence
  ): Promise<{
    approved: boolean;
    actualType: ContentType;
    reason: string;
  }> {
    // 1. 检查内容是否符合类型要求
    const requirements = CONTENT_TYPE_CONFIG[proposedType].requirements;
    const compliance = await this.checkTypeCompliance(contentId, requirements, evidence);
    
    // 2. 原创性检测
    const originalityCheck = await this.checkOriginality(contentId);
    
    // 3. 发布历史检查
    const publishHistory = await this.checkPublishHistory(contentId);
    
    // 4. 确定最终类型
    const actualType = this.determineActualType(
      proposedType,
      compliance,
      originalityCheck,
      publishHistory
    );
    
    return {
      approved: actualType === proposedType,
      actualType,
      reason: this.generateReason(actualType, compliance)
    };
  }
  
  // 内容类型调整
  static async adjustContentType(
    contentId: string,
    newType: ContentType,
    reason: string
  ): Promise<void> {
    // 更新内容类型
    await db.query(
      'UPDATE content_points_stats SET content_type = ? WHERE content_id = ?',
      [newType, contentId]
    );
    
    // 重新计算积分
    await this.recalculatePoints(contentId);
    
    // 记录调整日志
    await this.logTypeAdjustment(contentId, newType, reason);
  }
}
```

### 8.3 积分差异化策略
不同内容类型的积分差异化主要体现在：

1. **鼓励原创首发**：给予最高积分奖励
2. **支持内容分发**：适当降低积分，但仍有合理收益
3. **认可翻译贡献**：给予基础积分，鼓励优质翻译

这种策略平衡了：
- 创作者的创作积极性
- 平台内容的多样性
- 不同类型内容的价值贡献

## 10. 风险控制和防刷机制

### 10.1 互动质量识别
```typescript
class InteractionQualityAnalyzer {
  // 检测可疑点赞行为
  static async analyzeLikePattern(
    contentId: string, 
    likeEvents: LikeEvent[]
  ): Promise<{
    suspiciousCount: number;
    validCount: number;
    riskScore: number;
  }>;
  
  // 检测评论质量
  static async analyzeCommentQuality(
    comments: Comment[]
  ): Promise<{
    highQualityCount: number;
    lowQualityCount: number;
    spamCount: number;
  }>;
  
  // 检测阅读行为真实性
  static async analyzeViewPattern(
    viewEvents: ViewEvent[]
  ): Promise<{
    effectiveViews: number;
    bounceViews: number;
    suspiciousViews: number;
  }>;
}
```

### 10.2 用户行为风控
```typescript
class UserBehaviorRiskControl {
  // 用户积分获取异常检测
  static async detectAbnormalEarning(
    userId: string,
    timeWindow: number
  ): Promise<{
    isAbnormal: boolean;
    riskLevel: 'low' | 'medium' | 'high';
    suggestions: string[];
  }>;
  
  // 账户安全评估
  static async assessAccountSecurity(
    userId: string
  ): Promise<{
    securityScore: number;
    riskFactors: string[];
    restrictionLevel: number;
  }>;
}
```

## 11. 性能优化策略

### 11.1 缓存架构
```typescript
class PointsCacheManager {
  // 用户积分缓存（5分钟过期）
  static async getUserPointsCache(userId: string): Promise<PointsBalance>;
  
  // 内容实时统计缓存（避免重复查询业务表）
  static async getContentMetricsCache(contentId: string): Promise<ContentMetrics> {
    const cacheKey = `content_metrics:${contentId}`;
    const cached = await redis.get(cacheKey);
    
    if (cached) {
      return JSON.parse(cached);
    }
    
    // 缓存未命中，实时查询业务表
    const metrics = await PointsCalculationEngine.getContentMetrics(contentId);
    
    // 缓存5分钟，平衡实时性和性能
    await redis.setex(cacheKey, 300, JSON.stringify(metrics));
    
    return metrics;
  }
  
  // 积分配置缓存（1小时过期）
  static async getPointsConfigCache(): Promise<PointsConfig>;
  
  // 热门内容排行缓存（30分钟过期）
  static async getHotContentCache(): Promise<HotContent[]>;
}
```

### 11.2 数据库优化
```sql
-- 分区表设计（按月分区积分交易记录）
CREATE TABLE points_transactions_2024_01 PARTITION OF points_transactions
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 复合索引优化
CREATE INDEX idx_content_stats_author_points ON content_points_stats(author_id, total_points DESC);
CREATE INDEX idx_transactions_user_type_time ON points_transactions(user_id, type, created_at DESC);

-- 物化视图（用户积分汇总）
CREATE MATERIALIZED VIEW mv_user_points_summary AS
SELECT 
    user_id,
    SUM(CASE WHEN type = 'earn' THEN amount ELSE 0 END) as total_earned,
    SUM(CASE WHEN type = 'withdraw' THEN amount ELSE 0 END) as total_withdrawn,
    COUNT(*) as transaction_count,
    MAX(created_at) as last_activity
FROM points_transactions 
GROUP BY user_id;
```

## 12. 监控和数据分析

### 12.1 实时监控指标
```typescript
interface SystemMetrics {
  // 积分系统健康度
  pointsCalculationAccuracy: number;    // 积分计算准确率
  avgCalculationTime: number;           // 平均计算耗时
  
  // 用户参与度
  dailyActiveCreators: number;          // 日活跃创作者
  avgPointsPerCreator: number;          // 人均积分
  
  // 内容质量分布
  excellentContentRatio: number;        // 优秀内容占比
  avgPointsPerContent: number;          // 单篇内容平均积分
  
  // 提现情况
  dailyWithdrawalAmount: number;        // 日提现金额
  withdrawalSuccessRate: number;        // 提现成功率
}
```

### 12.2 数据分析报表
```typescript
class PointsAnalyticsService {
  // 创作者积分分析
  static async generateCreatorReport(timeRange: TimeRange): Promise<{
    topEarners: CreatorEarning[];
    contentTypeAnalysis: ContentTypeStats[];
    qualityDistribution: QualityDistribution;
  }>;
  
  // 内容表现分析
  static async generateContentReport(timeRange: TimeRange): Promise<{
    highPerformingContent: ContentPerformance[];
    categoryAnalysis: CategoryStats[];
    trendAnalysis: TrendData[];
  }>;
  
  // 系统财务分析
  static async generateFinancialReport(timeRange: TimeRange): Promise<{
    totalPointsIssued: number;
    totalWithdrawalAmount: number;
    avgCostPerContent: number;
    roi: number;
  }>;
}
```

## 13. Wise集成优势

### 13.0 Wise API文档参考

在实施Wise集成时，请参考以下官方文档：

#### 核心API文档
- **Wise API主页**: https://docs.wise.com/api-docs/
- **Quote API**: https://docs.wise.com/api-docs/api-reference/quote
- **Transfer API**: https://docs.wise.com/api-docs/api-reference/transfer
- **Recipient Account API**: https://docs.wise.com/api-docs/api-reference/recipient-account

#### 集成指南
- **快速开始**: https://docs.wise.com/api-docs/guides/getting-started
- **灵活定价配置**: https://docs.wise.com/api-docs/guides/flexible-partner-pricing
- **Webhook集成**: https://docs.wise.com/api-docs/guides/webhooks
- **错误处理**: https://docs.wise.com/api-docs/guides/error-handling

#### 认证和安全
- **API认证**: https://docs.wise.com/api-docs/guides/security-and-access
- **强客户认证(SCA)**: https://docs.wise.com/api-docs/guides/strong-customer-authentication
- **环境配置**: https://docs.wise.com/api-docs/guides/environments

#### 测试和调试
- **沙盒环境**: https://sandbox.transferwise.tech/
- **API测试工具**: https://docs.wise.com/api-docs/guides/simulation
- **状态码参考**: https://docs.wise.com/api-docs/guides/errors

### 13.1 简化的提现流程

通过集成Wise API，我们实现了完全自动化的提现流程：

#### 传统流程 vs Wise集成流程

**传统流程（复杂）：**
```
用户申请 → 填写银行信息 → 系统验证格式 → 管理员审核 → 手动创建转账 → 执行转账
```

**Wise集成流程（简化）：**
```
用户申请 → Wise自动创建转账单 → 管理员一键审核执行
```

### 13.2 核心优势

#### 13.2.1 用户体验优势
- **智能表单**：Wise API自动识别收款方式，用户只需填写必要信息
- **实时费用**：显示准确的手续费和到账金额
- **多货币支持**：支持全球180+国家和地区的货币
- **状态追踪**：实时同步转账状态，用户可随时查看进度

#### 13.2.2 管理优势
- **零配置**：无需维护复杂的银行信息验证规则
- **一键审核**：管理员只需审核用户身份，无需处理银行技术细节
- **自动执行**：审核通过后自动执行转账，无需手动操作
- **合规保障**：Wise处理所有反洗钱和合规检查

#### 13.2.3 技术优势
- **API优先**：完全通过API集成，无需人工干预
- **错误处理**：Wise API提供详细的错误信息和处理建议
- **状态同步**：通过Webhook实时同步转账状态
- **费用透明**：API返回详细的费用分解

### 13.3 工作流程详解

#### 13.3.1 用户提现申请
```typescript
// 用户提交提现申请
const withdrawalRequest = {
  userId: "user_123",
  amountPoints: 1500,
  targetCurrency: "CNY",
  recipientInfo: {
    fullName: "张三",
    accountType: "chinese_card",
    bankDetails: {
      // Wise会验证这些信息的格式
      accountNumber: "6228480402564890018",
      cnaps: "************"
    }
  }
};

// 系统自动调用Wise API
const result = await PointsAccountService.createWiseTransfer(
  withdrawalRequest.userId,
  15.00, // 转换为美元
  withdrawalRequest.recipientInfo
);
```

#### 13.3.2 管理员审核流程
```typescript
// 管理员查看待审核提现
GET /api/admin/withdrawals?status=wise_created

// 审核通过，一键执行转账
POST /api/admin/withdrawal/wd_001/approve
{
  "adminId": "admin_001",
  "notes": "用户身份验证通过"
}

// 系统自动调用Wise API执行转账
// 参考文档: https://docs.wise.com/api-docs/api-reference/transfer#fund-a-transfer
const fundingResult = await fetch(`https://api.wise.com/v3/profiles/${profileId}/transfers/${transferId}/payments`, {
  method: 'POST',
  body: JSON.stringify({ type: 'BALANCE' })
});
```

#### 13.3.3 状态同步机制
```typescript
// Wise Webhook处理转账状态更新
// 参考文档: https://docs.wise.com/api-docs/guides/webhooks
app.post('/webhooks/wise', (req, res) => {
  const { data } = req.body;
  
  if (data.resource.type === 'transfer') {
    // 更新系统中的转账状态
    await updateWithdrawalStatus(
      data.resource.id,
      data.current_state
    );
    
    // 通知用户状态变更
    await notifyUserStatusChange(data.resource.id);
  }
});
```

### 13.4 支持的收款方式

Wise API支持全球主要的收款方式，用户无需了解技术细节：

#### 13.4.1 中国大陆
- **银行卡转账**：支持所有主要银行
- **支付宝**：直接转账到支付宝账户
- **微信支付**：转账到微信钱包

#### 13.4.2 其他地区
- **美国**：ACH、Wire Transfer
- **欧盟**：SEPA转账
- **英国**：Faster Payments
- **印度**：UPI、IMPS
- **更多**：180+国家和地区

### 13.5 错误处理和降级方案

#### 13.5.1 API错误处理
```typescript
try {
  const transfer = await createWiseTransfer(userId, amount, recipientInfo);
  return { success: true, transfer };
} catch (error) {
  if (error.code === 'WISE_API_UNAVAILABLE') {
    // 降级到传统流程
    return await createManualWithdrawal(userId, amount, recipientInfo);
  }
  throw error;
}
```

#### 13.5.2 降级策略
- **API不可用**：自动切换到传统人工处理流程
- **费用异常**：使用静态费用计算作为备用
- **状态同步失败**：定期轮询Wise API获取状态

### 13.6 成本效益分析

#### 13.6.1 开发成本节省
- **减少50%开发时间**：无需开发复杂的银行信息验证
- **降低维护成本**：Wise处理所有银行接口变更
- **减少客服工作量**：自动化处理减少用户咨询

#### 13.6.2 用户体验提升
- **提现成功率提升30%**：Wise自动验证收款信息
- **处理时间缩短60%**：从平均3天缩短到1天
- **用户满意度提升**：透明的费用和状态追踪

#### 13.6.3 运营效率提升
- **管理员工作量减少70%**：从复杂审核简化为身份验证
- **错误率降低90%**：API自动处理技术细节
- **合规风险降低**：Wise承担反洗钱等合规责任

这种集成方案让积分系统的提现功能达到了银行级别的专业水准，同时大大简化了开发和运营复杂度。