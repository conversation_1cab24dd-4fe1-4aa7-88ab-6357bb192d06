# 积分系统定时任务说明

## 概述

本文档介绍了积分系统中每日自动计算积分的定时任务实现。定时任务会在每天凌晨2点自动运行，重新计算活跃内容的积分并更新用户积分余额。

## 核心特性

### 1. 智能积分计算
- **多维度互动数据**：点赞、评论、阅读、分享
- **内容类型差异化**：原创首发100%、原创分发60%、授权翻译30%
- **全局积分系数**：运营可动态调整整体激励力度
- **精选奖励**：固定2000积分奖励
- **管理员手动奖励**：完全自定义的奖励机制

### 2. 定时任务配置
- **执行时间**：每日凌晨2点（`0 0 2 * * *`）
- **处理范围**：最近30天内有互动的内容
- **错误处理**：单个内容计算失败不影响其他内容
- **执行报告**：详细的成功/失败统计

## 积分计算公式

```
总积分 = 内容类型调整后积分 + 精选奖励 + 管理员手动奖励

其中：
内容类型调整后积分 = (基础积分 × 全局系数) × 内容类型系数
基础积分 = 点赞数×3 + 评论数×10 + 阅读数×1 + 分享数×15
```

### 积分计算示例

#### 原创首发文章（目标8000积分）
```
互动数据：2000阅读、200点赞、50评论、30分享
基础积分：2000×1 + 200×3 + 50×10 + 30×15 = 3550
全局系数调整：3550 × 1.5 = 5325
内容类型调整：5325 × 1.0 = 5325（原创首发100%）
精选奖励：2000积分
管理员奖励：1000积分（可选）
总积分：5325 + 2000 + 1000 = 8325积分 ≈ 83美元
```

## 代码结构

### 1. 主要服务类
- `PointsCalculationService`：积分计算和定时任务的核心服务
- `ContentMetrics`：内容互动数据DTO
- `PointsCalculationResult`：积分计算结果DTO

### 2. Repository接口
- `ContentPointsStatsRepository`：内容积分统计
- `CreatorPointsRepository`：创作者积分账户
- `PointsTransactionRepository`：积分交易记录
- `PointsConfigRepository`：积分配置管理

### 3. Mapper接口
- `VoiceMapper`：内容数据查询
- `UserLikeMapper`：点赞数据统计
- `VoiceViewHistoryMapper`：浏览数据统计

## 数据库表结构

### 1. 创作者积分账户表 (creator_points)
```sql
CREATE TABLE creator_points (
    id bigserial PRIMARY KEY,
    user_id text UNIQUE NOT NULL,
    total_points bigint DEFAULT 0 NOT NULL,      -- 累计积分
    available_points bigint DEFAULT 0 NOT NULL,  -- 可提现积分
    frozen_points bigint DEFAULT 0 NOT NULL,     -- 冻结积分
    withdrawn_points bigint DEFAULT 0 NOT NULL,  -- 已提现积分
    last_calculated_at timestamp DEFAULT now(),
    created_at timestamp DEFAULT now(),
    updated_at timestamp DEFAULT now()
);
```

### 2. 积分交易记录表 (points_transactions)
```sql
CREATE TABLE points_transactions (
    id bigserial PRIMARY KEY,
    transaction_id text UNIQUE NOT NULL,
    user_id text NOT NULL,
    content_id text NULL,
    type varchar(20) NOT NULL,           -- 'earn' | 'withdraw' | 'adjust'
    sub_type varchar(50) NULL,           -- 'daily_settlement' | 'manual_bonus'
    amount bigint NOT NULL,              -- 积分金额(整数)
    balance bigint NOT NULL,             -- 交易后余额
    description varchar(500) NULL,
    metadata jsonb NULL,
    created_at timestamp DEFAULT now()
);
```

### 3. 内容积分统计表 (content_points_stats)
```sql
CREATE TABLE content_points_stats (
    id bigserial PRIMARY KEY,
    content_id text UNIQUE NOT NULL,
    author_id text NOT NULL,
    base_points bigint DEFAULT 0,              -- 基础积分
    feature_bonus bigint DEFAULT 0,            -- 精选奖励
    manual_bonus bigint DEFAULT 0,             -- 管理员手动奖励
    content_type_adjusted_points bigint DEFAULT 0, -- 内容类型调整后积分
    total_points bigint DEFAULT 0,             -- 总积分
    content_type varchar(20) DEFAULT 'original_first',
    is_featured boolean DEFAULT false,
    last_calculated_at timestamp DEFAULT now(),
    created_at timestamp DEFAULT now(),
    updated_at timestamp DEFAULT now()
);
```

## 配置参数

### 积分配置表 (points_config)
```sql
INSERT INTO points_config (config_key, config_value, description) VALUES
-- 基础积分权重
('points_per_like', '3', '每个点赞基础积分'),
('points_per_comment', '10', '每个评论基础积分'),
('points_per_view', '1', '每个有效阅读基础积分'),
('points_per_share', '15', '每个分享基础积分'),

-- 运营调整系数
('global_points_multiplier', '150', '全局积分系数(百分比)'),

-- 内容类型系数
('content_type_original_first', '100', '原创首发积分系数(百分比)'),
('content_type_original_repost', '60', '原创分发积分系数(百分比)'),
('content_type_authorized_translation', '30', '授权翻译积分系数(百分比)'),

-- 特殊奖励
('featured_bonus', '2000', '精选内容基础奖励积分');
```

## 使用方法

### 1. 启动定时任务
定时任务会在应用启动后自动运行。确保在主应用类中添加了 `@EnableScheduling` 注解：

```java
@SpringBootApplication
@MapperScan("com.panorai.mapper")
@EnableScheduling
public class Main {
    public static void main(String[] args) {
        SpringApplication.run(Main.class, args);
    }
}
```

### 2. 手动触发积分计算
```java
@Autowired
private PointsCalculationService pointsCalculationService;

// 手动执行每日积分结算
pointsCalculationService.dailyPointsSettlement();

// 计算单个内容的积分
pointsCalculationService.calculateAndUpdateContentPoints("content_id_123");
```

### 3. 监控任务执行
查看应用日志，定时任务会输出详细的执行报告：
```
2024-01-15 02:00:00 INFO  - 开始执行每日积分结算任务
2024-01-15 02:00:01 INFO  - 发现 158 个活跃内容需要重新计算积分
2024-01-15 02:02:30 INFO  - === 每日积分结算报告 ===
2024-01-15 02:02:30 INFO  - 处理成功: 156 个内容
2024-01-15 02:02:30 INFO  - 处理失败: 2 个内容
2024-01-15 02:02:30 INFO  - 成功率: 98.73%
2024-01-15 02:02:30 INFO  - 结算时间: 2024-01-15T02:02:30
```

## 注意事项

### 1. 性能考虑
- 定时任务限制处理最多1000个活跃内容
- 使用配置缓存减少数据库查询
- 单个内容计算失败不影响整体任务

### 2. 数据一致性
- 使用 `@Transactional` 确保积分计算的事务性
- 积分交易记录提供完整的审计跟踪
- 支持增量更新，避免重复计算

### 3. 扩展性
- 易于添加新的互动类型（如转发、收藏）
- 支持动态调整积分规则
- 可配置的内容类型和系数

### 4. 监控和排错
- 详细的日志记录
- 错误处理和恢复机制
- 积分计算结果的历史记录

## 后续优化

### 1. 缓存优化
- 热点内容积分缓存
- 用户积分余额缓存
- 配置参数缓存

### 2. 性能优化
- 批量处理优化
- 数据库索引优化
- 异步处理支持

### 3. 监控增强
- 积分计算异常告警
- 性能指标监控
- 业务指标统计

---

此定时任务实现了完整的积分计算和分发机制，支持多种内容类型和奖励方式，为创作者提供了公平透明的积分激励体系。 