-- 初始化积分系统配置数据

-- 插入积分配置数据
INSERT INTO points_config (config_key, config_value, description) VALUES
-- 基础积分权重 (保持稳定，不轻易修改)
('points_per_like', '3', '每个点赞基础积分'),
('points_per_comment', '10', '每个评论基础积分'),
('points_per_view', '1', '每个有效阅读基础积分'),
('points_per_share', '15', '每个分享基础积分'),

-- 运营调整系数 (运营可灵活调整)
('global_points_multiplier', '150', '全局积分系数(百分比，100=无调整)'),

-- 内容类型系数
('content_type_original_first', '100', '原创首发积分系数(百分比)'),
('content_type_original_repost', '60', '原创分发积分系数(百分比)'),
('content_type_authorized_translation', '30', '授权翻译积分系数(百分比)'),

-- 特殊奖励 (基础配置)
('featured_bonus', '2000', '精选内容基础奖励积分'),

-- 提现配置
('min_withdrawal_points', '1000', '最低提现积分'),
('max_withdrawal_points', '100000', '单次最高提现积分'),
('points_to_usd_rate', '100', '积分美元兑换比例(积分/美元)'),
('withdrawal_fee_fixed', '330', '固定手续费(美分)'),
('withdrawal_fee_rate', '100', '手续费比例(万分比)')
ON CONFLICT (config_key) DO NOTHING; 