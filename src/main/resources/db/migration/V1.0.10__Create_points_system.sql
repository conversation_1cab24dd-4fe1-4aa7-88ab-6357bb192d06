-- 创建积分系统相关表

-- 创作者积分账户表
CREATE TABLE IF NOT EXISTS creator_points (
    id bigserial NOT NULL,
    user_id text NOT NULL,
    total_points bigint DEFAULT 0 NOT NULL,
    available_points bigint DEFAULT 0 NOT NULL,
    frozen_points bigint DEFAULT 0 NOT NULL,
    withdrawn_points bigint DEFAULT 0 NOT NULL,
    last_calculated_at timestamp DEFAULT now() NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL,
    CONSTRAINT creator_points_pkey PRIMARY KEY (id),
    CONSTRAINT creator_points_user_id_unique UNIQUE (user_id)
);

CREATE INDEX IF NOT EXISTS creator_points_user_id_idx ON creator_points USING btree (user_id);
CREATE INDEX IF NOT EXISTS creator_points_available_points_idx ON creator_points USING btree (available_points);

-- 积分交易记录表
CREATE TABLE IF NOT EXISTS points_transactions (
    id bigserial NOT NULL,
    transaction_id text NOT NULL,
    user_id text NOT NULL,
    content_id text NULL,
    "type" varchar(20) NOT NULL,
    sub_type varchar(50) NULL,
    amount bigint NOT NULL,
    balance bigint NOT NULL,
    description varchar(500) NULL,
    metadata jsonb NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    CONSTRAINT points_transactions_pkey PRIMARY KEY (id),
    CONSTRAINT points_transactions_transaction_id_unique UNIQUE (transaction_id),
    CONSTRAINT points_transactions_type_check CHECK (type IN ('earn', 'withdraw', 'adjust', 'bonus'))
);

CREATE INDEX IF NOT EXISTS points_transactions_user_id_idx ON points_transactions USING btree (user_id);
CREATE INDEX IF NOT EXISTS points_transactions_content_id_idx ON points_transactions USING btree (content_id);
CREATE INDEX IF NOT EXISTS points_transactions_type_idx ON points_transactions USING btree (type);
CREATE INDEX IF NOT EXISTS points_transactions_user_type_time_idx ON points_transactions USING btree (user_id, type, created_at DESC);
CREATE INDEX IF NOT EXISTS points_transactions_created_at_idx ON points_transactions USING btree (created_at);

-- 内容积分统计表
CREATE TABLE IF NOT EXISTS content_points_stats (
    id bigserial NOT NULL,
    content_id text NOT NULL,
    author_id text NOT NULL,
    base_points bigint DEFAULT 0 NOT NULL,
    feature_bonus bigint DEFAULT 0 NOT NULL,
    manual_bonus bigint DEFAULT 0 NOT NULL,
    content_type_adjusted_points bigint DEFAULT 0 NOT NULL,
    total_points bigint DEFAULT 0 NOT NULL,
    content_type varchar(20) DEFAULT 'original_first' NOT NULL,
    is_featured bool DEFAULT false NOT NULL,
    last_calculated_at timestamp DEFAULT now() NOT NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL,
    CONSTRAINT content_points_stats_pkey PRIMARY KEY (id),
    CONSTRAINT content_points_stats_content_id_unique UNIQUE (content_id),
    CONSTRAINT content_points_stats_content_type_check CHECK (content_type IN ('original_first', 'original_repost', 'authorized_translation'))
);

CREATE INDEX IF NOT EXISTS content_points_stats_author_id_idx ON content_points_stats USING btree (author_id);
CREATE INDEX IF NOT EXISTS content_points_stats_total_points_idx ON content_points_stats USING btree (total_points DESC);
CREATE INDEX IF NOT EXISTS content_points_stats_author_points_idx ON content_points_stats USING btree (author_id, total_points DESC);
CREATE INDEX IF NOT EXISTS content_points_stats_content_type_idx ON content_points_stats USING btree (content_type);

-- 管理员手动奖励记录表
CREATE TABLE IF NOT EXISTS manual_bonus_records (
    id bigserial NOT NULL,
    content_id text NOT NULL,
    author_id text NOT NULL,
    admin_id text NOT NULL,
    bonus_points bigint NOT NULL,
    reason text NOT NULL,
    bonus_type varchar(20) DEFAULT 'quality' NOT NULL,
    status varchar(20) DEFAULT 'active' NOT NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL,
    CONSTRAINT manual_bonus_records_pkey PRIMARY KEY (id),
    CONSTRAINT manual_bonus_records_bonus_type_check CHECK (bonus_type IN ('quality', 'special', 'event', 'correction')),
    CONSTRAINT manual_bonus_records_status_check CHECK (status IN ('active', 'revoked'))
);

CREATE INDEX IF NOT EXISTS manual_bonus_records_content_id_idx ON manual_bonus_records USING btree (content_id);
CREATE INDEX IF NOT EXISTS manual_bonus_records_author_id_idx ON manual_bonus_records USING btree (author_id);
CREATE INDEX IF NOT EXISTS manual_bonus_records_admin_id_idx ON manual_bonus_records USING btree (admin_id);
CREATE INDEX IF NOT EXISTS manual_bonus_records_status_idx ON manual_bonus_records USING btree (status);

-- 积分配置表
CREATE TABLE IF NOT EXISTS points_config (
    id bigserial NOT NULL,
    config_key varchar(50) NOT NULL,
    config_value text NOT NULL,
    description varchar(200) NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL,
    CONSTRAINT points_config_pkey PRIMARY KEY (id),
    CONSTRAINT points_config_config_key_unique UNIQUE (config_key)
);

-- 提现申请表
CREATE TABLE IF NOT EXISTS withdrawal_requests (
    id bigserial NOT NULL,
    request_id text NOT NULL,
    user_id text NOT NULL,
    amount_points bigint NOT NULL,
    amount_usd numeric(10,2) NOT NULL,
    target_currency varchar(3) NOT NULL,
    target_amount numeric(15,2) NULL,
    status varchar(20) DEFAULT 'pending' NOT NULL,
    withdrawal_method varchar(50) DEFAULT 'wise' NOT NULL,
    wise_transfer_id text NULL,
    wise_quote_id text NULL,
    wise_recipient_id bigint NULL,
    wise_status varchar(50) NULL,
    wise_fee numeric(10,2) NULL,
    wise_exchange_rate numeric(15,6) NULL,
    recipient_info jsonb NOT NULL,
    admin_id text NULL,
    admin_notes text NULL,
    processed_at timestamp NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL,
    CONSTRAINT withdrawal_requests_pkey PRIMARY KEY (id),
    CONSTRAINT withdrawal_requests_request_id_unique UNIQUE (request_id),
    CONSTRAINT withdrawal_requests_status_check CHECK (status IN ('pending', 'wise_created', 'approved', 'rejected', 'completed'))
);

CREATE INDEX IF NOT EXISTS withdrawal_requests_user_id_idx ON withdrawal_requests USING btree (user_id);
CREATE INDEX IF NOT EXISTS withdrawal_requests_status_idx ON withdrawal_requests USING btree (status);
CREATE INDEX IF NOT EXISTS withdrawal_requests_user_status_idx ON withdrawal_requests USING btree (user_id, status);
CREATE INDEX IF NOT EXISTS withdrawal_requests_wise_transfer_idx ON withdrawal_requests USING btree (wise_transfer_id);
CREATE INDEX IF NOT EXISTS withdrawal_requests_created_at_idx ON withdrawal_requests USING btree (created_at);

-- 表注释
COMMENT ON TABLE creator_points IS '创作者积分账户表，记录用户的积分余额和状态';
COMMENT ON COLUMN creator_points.total_points IS '累计总积分（整数）';
COMMENT ON COLUMN creator_points.available_points IS '可提现积分';
COMMENT ON COLUMN creator_points.frozen_points IS '冻结积分';
COMMENT ON COLUMN creator_points.withdrawn_points IS '已提现积分';

COMMENT ON TABLE points_transactions IS '积分交易记录表，记录所有积分变动';
COMMENT ON COLUMN points_transactions.type IS '交易类型：earn获得、withdraw提现、adjust调整、bonus奖励';
COMMENT ON COLUMN points_transactions.sub_type IS '具体类型：like点赞、comment评论、quality_bonus质量奖励等';
COMMENT ON COLUMN points_transactions.amount IS '积分金额（整数）';
COMMENT ON COLUMN points_transactions.balance IS '交易后余额';

COMMENT ON TABLE content_points_stats IS '内容积分统计表，存储每个内容的积分计算结果';
COMMENT ON COLUMN content_points_stats.base_points IS '基础积分';
COMMENT ON COLUMN content_points_stats.feature_bonus IS '精选奖励';
COMMENT ON COLUMN content_points_stats.manual_bonus IS '管理员手动奖励';
COMMENT ON COLUMN content_points_stats.content_type_adjusted_points IS '内容类型调整后积分';
COMMENT ON COLUMN content_points_stats.total_points IS '总积分';
COMMENT ON COLUMN content_points_stats.content_type IS '内容类型：original_first原创首发、original_repost原创分发、authorized_translation授权翻译';

COMMENT ON TABLE manual_bonus_records IS '管理员手动奖励记录表';
COMMENT ON COLUMN manual_bonus_records.bonus_type IS '奖励类型：quality质量、special特殊、event活动、correction修正';
COMMENT ON COLUMN manual_bonus_records.status IS '状态：active生效、revoked已撤销';

COMMENT ON TABLE points_config IS '积分配置表，存储系统配置参数';

COMMENT ON TABLE withdrawal_requests IS '提现申请表，记录用户提现申请和处理状态';
COMMENT ON COLUMN withdrawal_requests.status IS '状态：pending待处理、wise_created已创建Wise转账、approved已批准、rejected已拒绝、completed已完成';
COMMENT ON COLUMN withdrawal_requests.recipient_info IS '收款人信息JSON'; 