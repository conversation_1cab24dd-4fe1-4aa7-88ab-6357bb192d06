-- Flyway migration script for QueerEcho user daily activity system
-- File: V1.0.10__Create_user_activity_system.sql

-- 用户每日活动记录表
CREATE TABLE IF NOT EXISTS user_daily_activity (
    id bigserial PRIMARY KEY,
    user_id TEXT NOT NULL,
    activity_date DATE NOT NULL,
    first_seen_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_seen_at TIMESTAMP WITH TIME ZONE NOT NULL,
    activity_count INTEGER DEFAULT 1,
    ip_addresses TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    CONSTRAINT unique_user_activity_date UNIQUE(user_id, activity_date)
);

-- 创建索引
CREATE INDEX idx_user_daily_activity_user_id ON user_daily_activity(user_id);
CREATE INDEX idx_user_daily_activity_date ON user_daily_activity(activity_date);
CREATE INDEX idx_user_daily_activity_user_date ON user_daily_activity(user_id, activity_date);

-- 表注释
COMMENT ON TABLE user_daily_activity IS '用户每日活动记录表，记录用户每天的上线活动情况';
COMMENT ON COLUMN user_daily_activity.user_id IS '用户ID';
COMMENT ON COLUMN user_daily_activity.activity_date IS '活动日期';
COMMENT ON COLUMN user_daily_activity.first_seen_at IS '当天首次活跃时间';
COMMENT ON COLUMN user_daily_activity.last_seen_at IS '当天最后活跃时间';
COMMENT ON COLUMN user_daily_activity.activity_count IS '当天活跃次数';
COMMENT ON COLUMN user_daily_activity.ip_addresses IS '当天使用的IP地址列表';
COMMENT ON COLUMN user_daily_activity.created_at IS '记录创建时间';
COMMENT ON COLUMN user_daily_activity.updated_at IS '记录更新时间';