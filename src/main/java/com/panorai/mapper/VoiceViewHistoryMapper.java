package com.panorai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panorai.model.dto.LastViewedTimeDTO;
import com.panorai.model.entity.VoiceViewHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface VoiceViewHistoryMapper extends BaseMapper<VoiceViewHistory> {

    /**
     * 获取指定voice IDs的最后浏览时间
     * @param voiceIds voice ID集合
     * @return Map<voiceId, lastViewedTime>
     */
    List<LastViewedTimeDTO> getLastViewedTime(@Param("voiceIds") Collection<String> voiceIds, @Param("userId") String userId);

    /**
     * 获取指定voice IDs的浏览统计信息（最后浏览时间 + 浏览次数）
     * @param voiceIds voice ID集合
     * @return List<Map<String, Object>> 包含voiceId, lastViewedTime, viewCount
     */
    List<LastViewedTimeDTO> getViewStatistics(@Param("voiceIds") Collection<String> voiceIds, @Param("userId") String userId);
}
