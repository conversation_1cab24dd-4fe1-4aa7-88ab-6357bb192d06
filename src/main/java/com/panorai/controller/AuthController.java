package com.panorai.controller;

import com.panorai.model.vo.InitPasswordRequest;
import com.panorai.model.vo.SignInWithOTPRequest;
import com.panorai.service.AuthService;
import com.panorai.utils.IpUtil;
import com.panorai.model.vo.SendOTPRequest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {
    private final AuthService authService;

    @PostMapping("/email-otp/send")
    public void sendOTP(HttpServletRequest httpServletRequest, @RequestBody SendOTPRequest request) {
        String ipAddress = IpUtil.getIpAddress(httpServletRequest);
        authService.sendEmailOTP(request.email(), ipAddress);
    }

    @PostMapping("/email-otp/sign-in")
    public String signInWithOTP(@RequestBody SignInWithOTPRequest request) {
        return authService.signInWithOTP(request.email(), request.otp());
    }

    @PostMapping("/init-password")
    public boolean initPassword(@RequestBody @Valid InitPasswordRequest request) {
        return authService.initPassword(request.userId(), request.password());
    }

    @PostMapping("/sign-out")
    public void signOut() {
        authService.signOut();
    }
}
