package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.mapper.UserDailyActivityMapper;
import com.panorai.model.entity.UserDailyActivity;
import com.panorai.repository.UserDailyActivityRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Optional;

@Slf4j
@Repository
public class UserDailyActivityRepositoryImpl extends ServiceImpl<UserDailyActivityMapper, UserDailyActivity> 
        implements UserDailyActivityRepository {

    @Override
    public Optional<UserDailyActivity> getByUserIdAndDate(String userId, LocalDate activityDate) {
        return this.lambdaQuery()
                .eq(UserDailyActivity::getUserId, userId)
                .eq(UserDailyActivity::getActivityDate, activityDate)
                .oneOpt();
    }

    @Override
    public void recordUserActivity(String userId, String ipAddress) {
        LocalDate today = LocalDate.now();
        
        Optional<UserDailyActivity> existingActivity = getByUserIdAndDate(userId, today);
        
        if (existingActivity.isPresent()) {
            // 更新现有记录
            UserDailyActivity activity = existingActivity.get();
            activity.updateActivity(ipAddress);
            this.updateById(activity);
            log.debug("Updated user activity for user: {}, count: {}", userId, activity.getActivityCount());
        } else {
            // 创建新记录
            UserDailyActivity newActivity = new UserDailyActivity(userId, ipAddress);
            this.save(newActivity);
            log.debug("Created new user activity record for user: {}", userId);
        }
    }
} 