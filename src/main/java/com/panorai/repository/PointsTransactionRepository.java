package com.panorai.repository;

import com.panorai.model.entity.PointsTransaction;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 积分交易记录Repository接口
 */
public interface PointsTransactionRepository {
    
    /**
     * 保存积分交易记录
     */
    void save(PointsTransaction transaction);
    
    /**
     * 根据用户ID查找交易记录
     */
    List<PointsTransaction> findByUserId(String userId, int limit);
    
    /**
     * 根据用户ID和交易类型查找记录
     */
    List<PointsTransaction> findByUserIdAndType(String userId, String type, int limit);
    
    /**
     * 根据时间范围查找交易记录
     */
    List<PointsTransaction> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计用户指定类型的积分总量
     */
    long sumAmountByUserIdAndType(String userId, String type);
    
    /**
     * 根据内容ID查找相关交易
     */
    List<PointsTransaction> findByContentId(String contentId);
} 