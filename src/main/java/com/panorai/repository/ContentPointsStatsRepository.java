package com.panorai.repository;

import com.panorai.model.entity.ContentPointsStats;

import java.util.List;

/**
 * 内容积分统计Repository接口
 */
public interface ContentPointsStatsRepository {
    
    /**
     * 根据内容ID查找积分统计
     */
    ContentPointsStats findByContentId(String contentId);
    
    /**
     * 保存积分统计
     */
    void save(ContentPointsStats stats);
    
    /**
     * 根据作者ID查找积分统计列表
     */
    List<ContentPointsStats> findByAuthorId(String authorId);
    
    /**
     * 查找总积分大于指定值的内容
     */
    List<ContentPointsStats> findByTotalPointsGreaterThan(long points);
    
    /**
     * 根据内容类型查找积分统计
     */
    List<ContentPointsStats> findByContentType(String contentType);
} 