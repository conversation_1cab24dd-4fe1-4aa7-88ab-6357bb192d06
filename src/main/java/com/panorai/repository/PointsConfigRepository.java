package com.panorai.repository;

import com.panorai.model.entity.PointsConfig;

import java.util.Map;

/**
 * 积分配置Repository接口
 */
public interface PointsConfigRepository {
    
    /**
     * 根据配置键查找配置
     */
    PointsConfig findByConfigKey(String configKey);
    
    /**
     * 获取所有配置的键值对
     */
    Map<String, String> findAllConfigs();
    
    /**
     * 保存或更新配置
     */
    void save(PointsConfig config);
    
    /**
     * 根据配置键获取配置值
     */
    String getConfigValue(String configKey);
    
    /**
     * 更新配置值
     */
    void updateConfigValue(String configKey, String configValue);
} 