package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.ArrayTypeHandler;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Objects;

@Data
@TableName("user_daily_activity")
@NoArgsConstructor
public class UserDailyActivity {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String userId;
    private LocalDate activityDate;
    private Instant firstSeenAt;
    private Instant lastSeenAt;
    private Integer activityCount;

    @TableField(typeHandler = ArrayTypeHandler.class)
    private String[] ipAddresses;
    private Instant createdAt;
    private Instant updatedAt;

    public UserDailyActivity(String userId, String ipAddress) {
        this.userId = userId;
        this.activityDate = LocalDate.now();
        this.firstSeenAt = Instant.now();
        this.lastSeenAt = Instant.now();
        this.activityCount = 1;
        this.ipAddresses = new String[]{ipAddress};
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }

    public void updateActivity(String ipAddress) {
        this.lastSeenAt = Instant.now();
        this.activityCount++;
        this.updatedAt = Instant.now();
        
        // 如果IP地址不在数组中，添加进去
        if (Objects.nonNull(this.ipAddresses) && Arrays.stream(this.ipAddresses).noneMatch(ip -> ip.equals(ipAddress))) {
            String[] newIpAddresses = new String[this.ipAddresses.length + 1];
            System.arraycopy(this.ipAddresses, 0, newIpAddresses, 0, this.ipAddresses.length);
            newIpAddresses[this.ipAddresses.length] = ipAddress;
            this.ipAddresses = newIpAddresses;
        }
    }
} 