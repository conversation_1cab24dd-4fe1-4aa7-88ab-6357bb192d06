package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@TableName("points_transactions")
@NoArgsConstructor
public class PointsTransaction {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String transactionId;
    
    private String userId;
    
    private String contentId;
    
    private String type;  // earn, withdraw, adjust, bonus
    
    private String subType;
    
    private Long amount;
    
    private Long balance;
    
    private String description;
    
    private String metadata;  // JSON字符串
    
    private Instant createdAt = Instant.now();
    
    public PointsTransaction(String transactionId, String userId, String type, Long amount, Long balance) {
        this.transactionId = transactionId;
        this.userId = userId;
        this.type = type;
        this.amount = amount;
        this.balance = balance;
        this.createdAt = Instant.now();
    }
} 