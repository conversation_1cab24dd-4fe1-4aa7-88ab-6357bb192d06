package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@TableName("creator_points")
@NoArgsConstructor
public class CreatorPoints {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String userId;
    
    private Long totalPoints = 0L;
    
    private Long availablePoints = 0L;
    
    private Long frozenPoints = 0L;
    
    private Long withdrawnPoints = 0L;
    
    private Instant lastCalculatedAt;
    
    private Instant createdAt = Instant.now();
    
    private Instant updatedAt = Instant.now();
    
    public CreatorPoints(String userId) {
        this.userId = userId;
        this.totalPoints = 0L;
        this.availablePoints = 0L;
        this.frozenPoints = 0L;
        this.withdrawnPoints = 0L;
        this.lastCalculatedAt = Instant.now();
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }
} 