package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@TableName("points_config")
@NoArgsConstructor
public class PointsConfig {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String configKey;
    
    private String configValue;
    
    private String description;
    
    private Instant createdAt = Instant.now();
    
    private Instant updatedAt = Instant.now();
    
    public PointsConfig(String configKey, String configValue, String description) {
        this.configKey = configKey;
        this.configValue = configValue;
        this.description = description;
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }
} 