package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@TableName("manual_bonus_records")
@NoArgsConstructor
public class ManualBonusRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String contentId;
    
    private String authorId;
    
    private String adminId;
    
    private Long bonusPoints;
    
    private String reason;
    
    private String bonusType = "quality";  // quality, special, event, correction
    
    private String status = "active";  // active, revoked
    
    private Instant createdAt = Instant.now();
    
    private Instant updatedAt = Instant.now();
    
    public ManualBonusRecord(String contentId, String authorId, String adminId, Long bonusPoints, String reason) {
        this.contentId = contentId;
        this.authorId = authorId;
        this.adminId = adminId;
        this.bonusPoints = bonusPoints;
        this.reason = reason;
        this.bonusType = "quality";
        this.status = "active";
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }
} 