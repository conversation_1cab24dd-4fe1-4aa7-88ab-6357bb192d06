package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@TableName("withdrawal_requests")
@NoArgsConstructor
public class WithdrawalRequest {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String requestId;
    
    private String userId;
    
    private Long amountPoints;
    
    private BigDecimal amountUsd;
    
    private String targetCurrency;
    
    private BigDecimal targetAmount;
    
    private String status = "pending";  // pending, wise_created, approved, rejected, completed
    
    private String withdrawalMethod = "wise";
    
    private String wiseTransferId;
    
    private String wiseQuoteId;
    
    private Long wiseRecipientId;
    
    private String wiseStatus;
    
    private BigDecimal wiseFee;
    
    private BigDecimal wiseExchangeRate;
    
    private String recipientInfo;  // JSON字符串
    
    private String adminId;
    
    private String adminNotes;
    
    private Instant processedAt;
    
    private Instant createdAt = Instant.now();
    
    private Instant updatedAt = Instant.now();
    
    public WithdrawalRequest(String requestId, String userId, Long amountPoints, BigDecimal amountUsd, String targetCurrency, String recipientInfo) {
        this.requestId = requestId;
        this.userId = userId;
        this.amountPoints = amountPoints;
        this.amountUsd = amountUsd;
        this.targetCurrency = targetCurrency;
        this.recipientInfo = recipientInfo;
        this.status = "pending";
        this.withdrawalMethod = "wise";
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }
} 