package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@TableName("content_points_stats")
@NoArgsConstructor
public class ContentPointsStats {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String contentId;
    
    private String authorId;
    
    private Long basePoints = 0L;
    
    private Long featureBonus = 0L;
    
    private Long manualBonus = 0L;
    
    private Long contentTypeAdjustedPoints = 0L;
    
    private Long totalPoints = 0L;
    
    private String contentType = "original_first";  // original_first, original_repost, authorized_translation
    
    private Boolean isFeatured = false;
    
    private Instant lastCalculatedAt = Instant.now();
    
    private Instant createdAt = Instant.now();
    
    private Instant updatedAt = Instant.now();
    
    public ContentPointsStats(String contentId, String authorId) {
        this.contentId = contentId;
        this.authorId = authorId;
        this.basePoints = 0L;
        this.featureBonus = 0L;
        this.manualBonus = 0L;
        this.contentTypeAdjustedPoints = 0L;
        this.totalPoints = 0L;
        this.contentType = "original_first";
        this.isFeatured = false;
        this.lastCalculatedAt = Instant.now();
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }
} 