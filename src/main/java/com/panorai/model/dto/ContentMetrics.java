package com.panorai.model.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 内容互动数据DTO
 */
@Data
@Builder
public class ContentMetrics {
    private String contentId;        // 内容ID
    private String authorId;         // 作者ID
    private int likeCount;          // 点赞数
    private int commentCount;       // 评论数
    private int viewCount;          // 阅读数
    private int shareCount;         // 分享数
    private int uniqueViewers;      // 独立访客数
    private double avgReadTime;     // 平均阅读时长
    private double bounceRate;      // 跳出率
    private String contentType;     // 内容类型: original_first, original_repost, authorized_translation
    private boolean isFeatured;     // 是否精选
} 