package com.panorai.model.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 积分计算结果DTO
 */
@Data
@Builder
public class PointsCalculationResult {
    private long basePoints;              // 基础积分
    private long adjustedBasePoints;      // 全局系数调整后的基础积分
    private double contentTypeMultiplier; // 内容类型系数
    private long contentTypeAdjustedPoints; // 内容类型调整后积分
    private long featuredBonus;           // 精选奖励
    private long manualBonus;            // 管理员手动奖励
    private long totalPoints;            // 总积分
} 