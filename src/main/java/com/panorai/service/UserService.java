package com.panorai.service;

import com.panorai.model.entity.User;
import com.panorai.model.entity.UserProfile;
import com.panorai.model.entity.Voice;
import com.panorai.repository.UserDailyActivityRepository;
import com.panorai.repository.UserProfileRepository;
import com.panorai.repository.UserRepository;
import com.panorai.repository.VoiceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserService {
    private final VoiceRepository voiceRepository;
    private final UserProfileRepository userProfileRepository;
    private final UserRepository userRepository;
    private final UserDailyActivityRepository userDailyActivityRepository;

    public UserProfile getProfileAndCheckActive(String userId) {
        UserProfile profile = userProfileRepository.optByUserId(userId).orElseGet(
                () -> createDefaultProfile(userId)
        );
        boolean isActive = profile.getActiveUser();
        profile.updateActive(checkActive(profile));
        if (!profile.getActiveUser().equals(isActive)) {
            userProfileRepository.updateById(profile);
        }
        return profile;
    }

    public UserProfile checkOrCreateAccount(String email) {
        Optional<User> userOpt = userRepository.getByEmail(email);
        if (userOpt.isEmpty()) {
            User user = new User(email);
            userRepository.save(user);
        }
        User user = userOpt.get();
        String userId = user.getId();
        return userProfileRepository.optByUserId(userId).orElseGet(
                () -> createDefaultProfile(userId)
        );
    }

    public UserProfile createDefaultProfile(String userId) {
        UserProfile defaultProfile = UserProfile.defaultProfile(userId);
        userProfileRepository.save(defaultProfile);
        return defaultProfile;
    }


    public Boolean checkActive(UserProfile profile) {
        Instant createdAt = profile.getCreatedAt();
        // 私密用户不能被判断活跃，对仅活跃用户可见的场景有风险
        if (!profile.getVisibility().equals("public")) {
            return false;
        }
        // 活跃用户必须注册超过30天
        if (createdAt.isAfter(Instant.now().minusSeconds(30 * 24 * 60 * 60))) {
            return false;
        }
        List<Voice> voices = voiceRepository.getLast30DaysVoices(profile.getId());

        // 如果没有voice，直接返回false
        if (voices.isEmpty()) {
            return false;
        }

        // 按创建时间排序（从早到晚）
        voices.sort(Comparator.comparing(Voice::getCreatedAt));

        // 检查是否有任何两个voice之间间隔超过72小时
        boolean hasTimeSpan = voices.get(0).getCreatedAt().isBefore(voices.get(voices.size() - 1).getCreatedAt().minusSeconds(72 * 60 * 60));

        // 如果所有voice都没有间隔72小时，直接返回false
        if (!hasTimeSpan) {
            return false;
        }

        // 正常计算分数
        Integer voiceScore = voices.stream().map(voice -> {
            if (voice.getIsReply()) {
                return 2;
            }
            if (voice.getIsRetweet()) {
                return 1;
            }
            if (voice.getType().equals("article")) {
                return 10;
            }
            return 6;
        }).reduce(0, Integer::sum);

        return voiceScore >= 30;
    }

    /**
     * 记录用户活动
     */
    public void recordUserActivity(String userId, String ipAddress) {
        try {
            userDailyActivityRepository.recordUserActivity(userId, ipAddress);
        } catch (Exception e) {
            log.error("Failed to record user activity for user: {}, error: {}", userId, e.getMessage());
            // 不抛出异常，避免影响主业务流程
        }
    }
}
