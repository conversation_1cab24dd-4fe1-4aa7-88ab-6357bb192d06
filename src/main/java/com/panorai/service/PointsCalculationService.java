package com.panorai.service;

import com.panorai.model.dto.ContentMetricsDTO;
import com.panorai.model.dto.PointsCalculationResult;
import com.panorai.model.entity.ContentPointsStats;
import com.panorai.model.entity.CreatorPoints;
import com.panorai.model.entity.PointsTransaction;
import com.panorai.repository.ContentPointsStatsRepository;
import com.panorai.repository.CreatorPointsRepository;
import com.panorai.repository.PointsTransactionRepository;
import com.panorai.repository.PointsConfigRepository;
import com.panorai.mapper.VoiceMapper;
import com.panorai.mapper.UserLikeMapper;
import com.panorai.mapper.VoiceViewHistoryMapper;
import com.panorai.utils.IdUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class PointsCalculationService {
    
    private static final Logger logger = LoggerFactory.getLogger(PointsCalculationService.class);
    
    @Autowired
    private VoiceMapper voiceMapper;
    
    @Autowired
    private UserLikeMapper userLikeMapper;
    
    @Autowired
    private VoiceViewHistoryMapper voiceViewHistoryMapper;
    
    @Autowired
    private ContentPointsStatsRepository contentPointsStatsRepository;
    
    @Autowired
    private CreatorPointsRepository creatorPointsRepository;
    
    @Autowired
    private PointsTransactionRepository pointsTransactionRepository;
    
    @Autowired
    private PointsConfigRepository pointsConfigRepository;
    
    // 缓存积分配置，避免重复查询
    private final Map<String, String> configCache = new ConcurrentHashMap<>();
    private LocalDateTime lastConfigUpdate = LocalDateTime.now();
    
    /**
     * 每日凌晨2点执行积分结算
     */
    @Scheduled(cron = "0 0 2 * * *")
    @Transactional
    public void dailyPointsSettlement() {
        logger.info("开始执行每日积分结算任务");
        
        try {
            // 1. 刷新积分配置缓存
            refreshConfigCache();
            
            // 2. 获取需要重新计算的活跃内容
            List<String> activeContentIds = getActiveContentIds();
            logger.info("发现 {} 个活跃内容需要重新计算积分", activeContentIds.size());
            
            int successCount = 0;
            int errorCount = 0;
            
            // 3. 逐个处理内容积分计算
            for (String contentId : activeContentIds) {
                try {
                    calculateAndUpdateContentPoints(contentId);
                    successCount++;
                } catch (Exception e) {
                    logger.error("内容 {} 积分计算失败: {}", contentId, e.getMessage(), e);
                    errorCount++;
                }
            }
            
            // 4. 生成结算报告
            generateSettlementReport(successCount, errorCount);
            
            logger.info("每日积分结算任务完成，成功: {}, 失败: {}", successCount, errorCount);
            
        } catch (Exception e) {
            logger.error("每日积分结算任务执行失败", e);
        }
    }
    
    /**
     * 计算并更新单个内容的积分
     */
    @Transactional
    public void calculateAndUpdateContentPoints(String contentId) {
        logger.debug("开始计算内容 {} 的积分", contentId);
        
        // 1. 获取内容互动数据
        ContentMetricsDTO metrics = getContentMetrics(contentId);
        if (metrics == null) {
            logger.warn("无法获取内容 {} 的互动数据", contentId);
            return;
        }
        
        // 2. 获取管理员手动奖励
        long manualBonus = getManualBonusPoints(contentId);
        
        // 3. 计算积分
        PointsCalculationResult result = calculatePoints(metrics, manualBonus);
        
        // 4. 更新或创建内容积分统计
        updateContentPointsStats(contentId, metrics, result);
        
        // 5. 更新用户积分余额
        updateUserPointsBalance(metrics.getAuthorId(), result);
        
        logger.debug("内容 {} 积分计算完成，总积分: {}", contentId, result.getTotalPoints());
    }
    
    /**
     * 获取内容互动数据
     */
    private ContentMetricsDTO getContentMetrics(String contentId) {
        try {
            // 从Voice表获取基础信息
            var voice = voiceMapper.findById(contentId);
            if (voice == null) {
                return null;
            }
            
            // 统计点赞数
            int likeCount = userLikeMapper.countByTargetId(contentId);
            
            // 统计评论数（这里假设有评论功能，需要根据实际情况调整）
            int commentCount = 0; // TODO: 需要根据实际评论表实现
            
            // 统计阅读数和分享数
            int viewCount = voiceViewHistoryMapper.countByVoiceId(contentId);
            int shareCount = 0; // TODO: 需要根据实际分享表实现
            
            return ContentMetricsDTO.builder()
                    .contentId(contentId)
                    .authorId(voice.getUserId())
                    .likeCount(likeCount)
                    .commentCount(commentCount)
                    .viewCount(viewCount)
                    .shareCount(shareCount)
                    .contentType(determineContentType(voice))
                    .isFeatured(voice.getIsFeatured() != null && voice.getIsFeatured())
                    .build();
                    
        } catch (Exception e) {
            logger.error("获取内容 {} 互动数据失败", contentId, e);
            return null;
        }
    }
    
    /**
     * 计算积分
     */
    private PointsCalculationResult calculatePoints(ContentMetricsDTO metrics, long manualBonus) {
        // 1. 获取积分配置
        int pointsPerLike = getConfigInt("points_per_like", 3);
        int pointsPerComment = getConfigInt("points_per_comment", 10);
        int pointsPerView = getConfigInt("points_per_view", 1);
        int pointsPerShare = getConfigInt("points_per_share", 15);
        int globalMultiplier = getConfigInt("global_points_multiplier", 150);
        int featuredBonus = getConfigInt("featured_bonus", 2000);
        
        // 2. 计算基础积分
        long basePoints = (long) metrics.getLikeCount() * pointsPerLike +
                         (long) metrics.getCommentCount() * pointsPerComment +
                         (long) metrics.getViewCount() * pointsPerView +
                         (long) metrics.getShareCount() * pointsPerShare;
        
        // 3. 应用全局积分系数
        long adjustedBasePoints = (basePoints * globalMultiplier) / 100;
        
        // 4. 获取内容类型系数
        double contentTypeMultiplier = getContentTypeMultiplier(metrics.getContentType());
        
        // 5. 应用内容类型系数
        long contentTypeAdjustedPoints = (long) (adjustedBasePoints * contentTypeMultiplier);
        
        // 6. 计算精选奖励
        long featuredBonusPoints = 0;
        if (metrics.isFeatured()) {
            featuredBonusPoints = (long) (featuredBonus * contentTypeMultiplier);
        }
        
        // 7. 计算总积分
        long totalPoints = contentTypeAdjustedPoints + featuredBonusPoints + manualBonus;
        
        return PointsCalculationResult.builder()
                .basePoints(basePoints)
                .adjustedBasePoints(adjustedBasePoints)
                .contentTypeMultiplier(contentTypeMultiplier)
                .contentTypeAdjustedPoints(contentTypeAdjustedPoints)
                .featuredBonus(featuredBonusPoints)
                .manualBonus(manualBonus)
                .totalPoints(totalPoints)
                .build();
    }
    
    /**
     * 更新内容积分统计
     */
    private void updateContentPointsStats(String contentId, ContentMetricsDTO metrics, PointsCalculationResult result) {
        ContentPointsStats stats = contentPointsStatsRepository.findByContentId(contentId);
        
        if (stats == null) {
            // 创建新记录
            stats = new ContentPointsStats();
            stats.setContentId(contentId);
            stats.setAuthorId(metrics.getAuthorId());
            stats.setCreatedAt(LocalDateTime.now());
        }
        
        // 更新积分数据
        stats.setBasePoints(result.getBasePoints());
        stats.setFeatureBonus(result.getFeaturedBonus());
        stats.setManualBonus(result.getManualBonus());
        stats.setContentTypeAdjustedPoints(result.getContentTypeAdjustedPoints());
        stats.setTotalPoints(result.getTotalPoints());
        stats.setContentType(metrics.getContentType());
        stats.setIsFeatured(metrics.isFeatured());
        stats.setLastCalculatedAt(LocalDateTime.now());
        stats.setUpdatedAt(LocalDateTime.now());
        
        contentPointsStatsRepository.save(stats);
    }
    
    /**
     * 更新用户积分余额
     */
    private void updateUserPointsBalance(String userId, PointsCalculationResult result) {
        // 获取或创建用户积分账户
        CreatorPoints account = creatorPointsRepository.findByUserId(userId);
        if (account == null) {
            account = new CreatorPoints();
            account.setUserId(userId);
            account.setTotalPoints(0L);
            account.setAvailablePoints(0L);
            account.setFrozenPoints(0L);
            account.setWithdrawnPoints(0L);
            account.setCreatedAt(LocalDateTime.now());
        }
        
        // 计算积分变化
        long previousTotal = account.getTotalPoints();
        long newTotal = result.getTotalPoints();
        long deltaPoints = newTotal - previousTotal;
        
        if (deltaPoints != 0) {
            // 更新积分余额
            account.setTotalPoints(account.getTotalPoints() + deltaPoints);
            account.setAvailablePoints(account.getAvailablePoints() + deltaPoints);
            account.setLastCalculatedAt(LocalDateTime.now());
            account.setUpdatedAt(LocalDateTime.now());
            
            creatorPointsRepository.save(account);
            
            // 记录积分交易
            recordPointsTransaction(userId, deltaPoints, account.getTotalPoints(), "daily_settlement");
        }
    }
    
    /**
     * 记录积分交易
     */
    private void recordPointsTransaction(String userId, long amount, long balance, String description) {
        PointsTransaction transaction = new PointsTransaction();
        transaction.setTransactionId(IdUtils.generateId());
        transaction.setUserId(userId);
        transaction.setType(amount > 0 ? "earn" : "adjust");
        transaction.setSubType("daily_settlement");
        transaction.setAmount(amount);
        transaction.setBalance(balance);
        transaction.setDescription(description);
        transaction.setCreatedAt(LocalDateTime.now());
        
        pointsTransactionRepository.save(transaction);
    }
    
    /**
     * 获取活跃内容ID列表
     */
    private List<String> getActiveContentIds() {
        // 这里可以根据业务需求调整查询条件
        // 例如：最近30天有互动的内容，或者特定状态的内容
        return voiceMapper.findActiveContentIds();
    }
    
    /**
     * 获取管理员手动奖励积分
     */
    private long getManualBonusPoints(String contentId) {
        // TODO: 实现获取管理员手动奖励的逻辑
        return 0L;
    }
    
    /**
     * 确定内容类型
     */
    private String determineContentType(Object voice) {
        // TODO: 根据Voice对象的属性确定内容类型
        // 默认返回原创首发
        return "original_first";
    }
    
    /**
     * 获取内容类型积分系数
     */
    private double getContentTypeMultiplier(String contentType) {
        switch (contentType) {
            case "original_first":
                return getConfigInt("content_type_original_first", 100) / 100.0;
            case "original_repost":
                return getConfigInt("content_type_original_repost", 60) / 100.0;
            case "authorized_translation":
                return getConfigInt("content_type_authorized_translation", 30) / 100.0;
            default:
                return 1.0;
        }
    }
    
    /**
     * 获取配置值（整数）
     */
    private int getConfigInt(String key, int defaultValue) {
        String value = configCache.get(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            logger.warn("配置 {} 的值 {} 不是有效整数，使用默认值 {}", key, value, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 刷新配置缓存
     */
    private void refreshConfigCache() {
        try {
            Map<String, String> configs = pointsConfigRepository.findAllConfigs();
            configCache.clear();
            configCache.putAll(configs);
            lastConfigUpdate = LocalDateTime.now();
            logger.info("积分配置缓存已刷新，共 {} 个配置项", configs.size());
        } catch (Exception e) {
            logger.error("刷新积分配置缓存失败", e);
        }
    }
    
    /**
     * 生成结算报告
     */
    private void generateSettlementReport(int successCount, int errorCount) {
        logger.info("=== 每日积分结算报告 ===");
        logger.info("处理成功: {} 个内容", successCount);
        logger.info("处理失败: {} 个内容", errorCount);
        logger.info("成功率: {}%", successCount * 100.0 / (successCount + errorCount));
        logger.info("结算时间: {}", LocalDateTime.now());
        logger.info("========================");
    }
} 